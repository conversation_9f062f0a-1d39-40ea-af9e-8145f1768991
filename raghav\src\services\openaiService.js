import OpenAI from 'openai';

// Initialize OpenAI client
let openai = null;

const initializeOpenAI = (apiKey) => {
  if (!openai) {
    openai = new OpenAI({
      apiKey: apiKey,
      dangerouslyAllowBrowser: true // Note: In production, use a backend server
    });
  }
  return openai;
};

/**
 * Extract most important points from document using OpenAI
 */
export const analyzeDocumentWithOpenAI = async (text, apiKey, maxPoints = 5) => {
  try {
    if (!apiKey) {
      throw new Error('OpenAI API key is required');
    }

    const client = initializeOpenAI(apiKey);

    const prompt = `
Please analyze the following document and extract only the ${maxPoints} MOST IMPORTANT points. 
Focus on:
- Key facts and findings
- Main conclusions or recommendations  
- Critical information that someone must know
- Important statistics or data
- Essential takeaways

Return the response in this exact JSON format:
{
  "importantPoints": [
    {
      "id": 1,
      "text": "First most important point",
      "importance": "High"
    },
    {
      "id": 2, 
      "text": "Second most important point",
      "importance": "High"
    }
  ],
  "keywords": [
    {"word": "keyword1", "frequency": 5},
    {"word": "keyword2", "frequency": 3}
  ]
}

Document to analyze:
${text}
`;

    const response = await client.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: "You are an expert document analyzer. Extract only the most critical and important points from documents. Be selective and focus on what truly matters."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      max_tokens: 1500,
      temperature: 0.3
    });

    const result = response.choices[0].message.content;
    
    // Parse the JSON response
    try {
      const parsedResult = JSON.parse(result);
      
      // Validate the structure
      if (!parsedResult.importantPoints || !Array.isArray(parsedResult.importantPoints)) {
        throw new Error('Invalid response format from OpenAI');
      }

      // Ensure we don't exceed maxPoints
      parsedResult.importantPoints = parsedResult.importantPoints.slice(0, maxPoints);
      
      return {
        importantPoints: parsedResult.importantPoints,
        keywords: parsedResult.keywords || [],
        summary: {
          totalSentences: 0, // Not needed anymore
          totalWords: 0,     // Not needed anymore  
          selectedSentences: parsedResult.importantPoints.length
        }
      };
    } catch (parseError) {
      console.error('Error parsing OpenAI response:', parseError);
      throw new Error('Failed to parse analysis results');
    }

  } catch (error) {
    console.error('OpenAI API Error:', error);
    
    if (error.message.includes('API key')) {
      throw new Error('Invalid or missing OpenAI API key');
    } else if (error.message.includes('quota')) {
      throw new Error('OpenAI API quota exceeded. Please check your billing.');
    } else if (error.message.includes('rate limit')) {
      throw new Error('Rate limit exceeded. Please try again in a moment.');
    } else {
      throw new Error('Failed to analyze document with OpenAI: ' + error.message);
    }
  }
};

/**
 * Process uploaded file with OpenAI
 */
export const processFileWithOpenAI = async (file, apiKey, maxPoints = 5) => {
  try {
    // Convert file to text based on type
    let text = '';
    
    if (file.type === 'text/plain') {
      text = await file.text();
    } else if (file.type === 'application/pdf') {
      // For PDF files, we'll use a simple approach
      // In production, you might want to use a backend service for PDF processing
      throw new Error('PDF processing with OpenAI requires backend implementation for security. Please convert to text format.');
    } else {
      throw new Error('Unsupported file type. Please upload a text file.');
    }

    if (!text || text.trim().length === 0) {
      throw new Error('No text content found in the file');
    }

    // Check text length (OpenAI has token limits)
    if (text.length > 12000) { // Rough estimate for token limit
      text = text.substring(0, 12000) + '...';
    }

    return await analyzeDocumentWithOpenAI(text, apiKey, maxPoints);
    
  } catch (error) {
    throw error;
  }
};
