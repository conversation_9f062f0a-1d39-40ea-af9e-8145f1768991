import OpenAI from 'openai';

// Initialize OpenAI client
let openai = null;

const initializeOpenAI = (apiKey) => {
  if (!openai) {
    openai = new OpenAI({
      apiKey: apiKey,
      dangerouslyAllowBrowser: true // Note: In production, use a backend server
    });
  }
  return openai;
};

/**
 * Extract most important points from document using OpenAI
 */
export const analyzeDocumentWithOpenAI = async (text, apiKey, maxPoints = 5) => {
  try {
    if (!apiKey) {
      throw new Error('OpenAI API key is required');
    }

    const client = initializeOpenAI(apiKey);

    const prompt = `
Please analyze the following document and extract only the ${maxPoints} MOST IMPORTANT points. 
Focus on:
- Key facts and findings
- Main conclusions or recommendations  
- Critical information that someone must know
- Important statistics or data
- Essential takeaways

Return the response in this exact JSON format:
{
  "importantPoints": [
    {
      "id": 1,
      "text": "First most important point",
      "importance": "High"
    },
    {
      "id": 2, 
      "text": "Second most important point",
      "importance": "High"
    }
  ],
  "keywords": [
    {"word": "keyword1", "frequency": 5},
    {"word": "keyword2", "frequency": 3}
  ]
}

Document to analyze:
${text}
`;

    const response = await client.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: "You are an expert document analyzer. Extract only the most critical and important points from documents. Be selective and focus on what truly matters."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      max_tokens: 1500,
      temperature: 0.3
    });

    const result = response.choices[0].message.content;
    
    // Parse the JSON response
    try {
      const parsedResult = JSON.parse(result);
      
      // Validate the structure
      if (!parsedResult.importantPoints || !Array.isArray(parsedResult.importantPoints)) {
        throw new Error('Invalid response format from OpenAI');
      }

      // Ensure we don't exceed maxPoints
      parsedResult.importantPoints = parsedResult.importantPoints.slice(0, maxPoints);
      
      return {
        importantPoints: parsedResult.importantPoints,
        keywords: parsedResult.keywords || [],
        summary: {
          totalSentences: 0, // Not needed anymore
          totalWords: 0,     // Not needed anymore  
          selectedSentences: parsedResult.importantPoints.length
        }
      };
    } catch (parseError) {
      console.error('Error parsing OpenAI response:', parseError);
      throw new Error('Failed to parse analysis results');
    }

  } catch (error) {
    console.error('OpenAI API Error:', error);
    
    if (error.message.includes('API key')) {
      throw new Error('Invalid or missing OpenAI API key');
    } else if (error.message.includes('quota')) {
      throw new Error('OpenAI API quota exceeded. Please check your billing.');
    } else if (error.message.includes('rate limit')) {
      throw new Error('Rate limit exceeded. Please try again in a moment.');
    } else {
      throw new Error('Failed to analyze document with OpenAI: ' + error.message);
    }
  }
};

/**
 * Extract text from PDF using PDF.js
 */
const extractTextFromPDF = async (file) => {
  // Load PDF.js if not already loaded
  if (!window.pdfjsLib) {
    await loadPDFJS();
  }

  const arrayBuffer = await file.arrayBuffer();
  const pdf = await window.pdfjsLib.getDocument({ data: arrayBuffer }).promise;
  let fullText = '';

  for (let i = 1; i <= pdf.numPages; i++) {
    const page = await pdf.getPage(i);
    const textContent = await page.getTextContent();
    const pageText = textContent.items.map(item => item.str).join(' ');
    fullText += pageText + '\n';
  }

  return fullText;
};

/**
 * Load PDF.js library from CDN
 */
const loadPDFJS = () => {
  return new Promise((resolve, reject) => {
    if (window.pdfjsLib) {
      resolve();
      return;
    }

    const script = document.createElement('script');
    script.src = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js';
    script.onload = () => {
      window.pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
      resolve();
    };
    script.onerror = () => reject(new Error('Failed to load PDF.js library'));
    document.head.appendChild(script);
  });
};

/**
 * Process uploaded file with OpenAI
 */
export const processFileWithOpenAI = async (file, apiKey, maxPoints = 5) => {
  try {
    // Convert file to text based on type
    let text = '';

    if (file.type === 'text/plain' || file.name.toLowerCase().endsWith('.txt')) {
      text = await file.text();
    } else if (file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf')) {
      try {
        text = await extractTextFromPDF(file);
      } catch (pdfError) {
        throw new Error('Failed to extract text from PDF. Please ensure the PDF contains readable text (not just images).');
      }
    } else {
      throw new Error('Unsupported file type. Please upload a PDF or TXT file.');
    }

    if (!text || text.trim().length === 0) {
      throw new Error('No text content found in the file. The document may be empty or contain only images.');
    }

    // Check text length (OpenAI has token limits)
    if (text.length > 12000) { // Rough estimate for token limit
      text = text.substring(0, 12000) + '...';
    }

    return await analyzeDocumentWithOpenAI(text, apiKey, maxPoints);

  } catch (error) {
    throw error;
  }
};
