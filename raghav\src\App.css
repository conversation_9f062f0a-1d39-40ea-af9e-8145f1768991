/* App Layout */
.app {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.app-header {
  text-align: center;
  margin-bottom: 40px;
  color: white;
}

.app-header h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  font-weight: 700;
}

.app-header p {
  font-size: 1.1rem;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
}

.app-main {
  max-width: 1200px;
  margin: 0 auto;
}

/* Error Message */
.error-message {
  background: #fee;
  border: 1px solid #fcc;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  color: #c33;
}

.error-icon {
  font-size: 1.2rem;
}

.error-close {
  margin-left: auto;
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #c33;
}

/* File Upload Component */
.file-upload-container {
  margin-bottom: 30px;
}

.dropzone {
  border: 3px dashed #ddd;
  border-radius: 12px;
  padding: 40px;
  text-align: center;
  background: white;
  transition: all 0.3s ease;
  cursor: pointer;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dropzone:hover {
  border-color: #667eea;
  background: #f8f9ff;
}

.dropzone.active {
  border-color: #667eea;
  background: #f0f4ff;
  transform: scale(1.02);
}

.dropzone.processing {
  border-color: #f39c12;
  background: #fff9f0;
  cursor: not-allowed;
}

.upload-content {
  width: 100%;
}

.upload-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

.upload-instructions h3 {
  margin-bottom: 10px;
  color: #333;
  font-size: 1.5rem;
}

.upload-instructions p {
  color: #666;
  margin-bottom: 10px;
  font-size: 1.1rem;
}

.supported-formats {
  font-size: 0.9rem;
  color: #999;
}

.file-info {
  margin-top: 10px;
}

.file-name {
  font-weight: bold;
  color: #333;
  font-size: 1.1rem;
}

.file-size {
  color: #666;
  font-size: 0.9rem;
  margin: 5px 0;
}

.upload-instruction {
  color: #999;
  font-size: 0.9rem;
}

.processing-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.file-actions {
  margin-top: 15px;
  text-align: center;
}

.clear-btn {
  background: #e74c3c;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
}

.clear-btn:hover {
  background: #c0392b;
}

/* Welcome Message */
.welcome-message {
  background: white;
  border-radius: 12px;
  padding: 40px;
  text-align: center;
  margin-top: 30px;
}

.welcome-message h2 {
  color: #333;
  margin-bottom: 15px;
  font-size: 1.8rem;
}

.welcome-message p {
  color: #666;
  font-size: 1.1rem;
  margin-bottom: 30px;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.features {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
}

.feature {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #555;
  font-weight: 500;
}

.feature-icon {
  font-size: 1.2rem;
}

/* Results Display */
.results-container {
  background: white;
  border-radius: 12px;
  padding: 30px;
  margin-top: 30px;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  flex-wrap: wrap;
  gap: 15px;
}

.results-header h2 {
  color: #333;
  font-size: 1.8rem;
  margin: 0;
}

.file-info .file-name {
  color: #666;
  font-size: 1rem;
}

.summary-stats {
  display: flex;
  gap: 30px;
  margin-bottom: 30px;
  justify-content: center;
  flex-wrap: wrap;
}

.stat {
  text-align: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  min-width: 120px;
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: bold;
  color: #667eea;
}

.stat-label {
  font-size: 0.9rem;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tabs {
  display: flex;
  border-bottom: 2px solid #eee;
  margin-bottom: 25px;
}

.tab {
  background: none;
  border: none;
  padding: 12px 20px;
  cursor: pointer;
  font-size: 1rem;
  color: #666;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
}

.tab:hover {
  color: #667eea;
}

.tab.active {
  color: #667eea;
  border-bottom-color: #667eea;
  font-weight: 600;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  color: #333;
  margin: 0;
}

.export-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
}

.export-btn:hover {
  background: #218838;
}

.points-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.point-item {
  border: 1px solid #eee;
  border-radius: 8px;
  padding: 20px;
  background: #fafafa;
}

.point-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.point-number {
  background: #667eea;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: bold;
}

.importance-badge {
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: bold;
}

.copy-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  margin-left: auto;
  padding: 4px;
  border-radius: 4px;
}

.copy-btn:hover {
  background: #eee;
}

.point-text {
  color: #333;
  line-height: 1.6;
  margin: 10px 0;
  font-size: 1rem;
}

.point-score {
  font-size: 0.8rem;
  color: #999;
}

.keywords-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.keyword-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.keyword-text {
  font-weight: 500;
  color: #333;
}

.keyword-frequency {
  background: #667eea;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: bold;
}

.no-results {
  text-align: center;
  padding: 40px;
  color: #666;
}

/* API Key Input Styles */
.api-key-container {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
}

.api-key-card {
  background: white;
  border-radius: 12px;
  padding: 30px;
  max-width: 500px;
  width: 100%;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.api-key-card h3 {
  color: #333;
  margin-bottom: 10px;
  text-align: center;
}

.api-key-card p {
  color: #666;
  text-align: center;
  margin-bottom: 25px;
}

.api-key-form {
  margin-bottom: 25px;
}

.input-group {
  display: flex;
  margin-bottom: 15px;
}

.api-key-input {
  flex: 1;
  padding: 12px;
  border: 2px solid #ddd;
  border-radius: 6px 0 0 6px;
  font-size: 1rem;
  font-family: monospace;
}

.api-key-input:focus {
  outline: none;
  border-color: #667eea;
}

.toggle-visibility {
  padding: 12px 15px;
  border: 2px solid #ddd;
  border-left: none;
  border-radius: 0 6px 6px 0;
  background: #f8f9fa;
  cursor: pointer;
}

.toggle-visibility:hover {
  background: #e9ecef;
}

.submit-btn {
  width: 100%;
  padding: 12px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  cursor: pointer;
  font-weight: 600;
}

.submit-btn:hover:not(:disabled) {
  background: #5a6fd8;
}

.submit-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.api-key-info {
  border-top: 1px solid #eee;
  padding-top: 20px;
}

.api-key-info h4 {
  color: #333;
  margin-bottom: 10px;
  font-size: 1rem;
}

.api-key-info ol {
  color: #666;
  padding-left: 20px;
  margin-bottom: 15px;
}

.api-key-info li {
  margin-bottom: 5px;
}

.api-key-info a {
  color: #667eea;
  text-decoration: none;
}

.api-key-info a:hover {
  text-decoration: underline;
}

.security-note {
  background: #f0f4ff;
  border: 1px solid #d1e7ff;
  border-radius: 6px;
  padding: 12px;
  font-size: 0.9rem;
  color: #0066cc;
}

/* Settings Bar */
.settings-bar {
  background: white;
  border-radius: 8px;
  padding: 15px 20px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.api-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  font-size: 0.8rem;
}

.change-key-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  cursor: pointer;
  margin-left: 10px;
}

.change-key-btn:hover {
  background: #5a6268;
}

.max-points-setting {
  display: flex;
  align-items: center;
  gap: 8px;
}

.max-points-setting label {
  font-weight: 500;
  color: #333;
}

.max-points-setting select {
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  cursor: pointer;
}

.max-points-setting select:focus {
  outline: none;
  border-color: #667eea;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app {
    padding: 10px;
  }

  .app-header h1 {
    font-size: 2rem;
  }

  .dropzone {
    padding: 20px;
    min-height: 150px;
  }

  .results-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .summary-stats {
    gap: 15px;
  }

  .features {
    flex-direction: column;
    gap: 15px;
  }

  .keywords-grid {
    grid-template-columns: 1fr;
  }

  .api-key-card {
    padding: 20px;
    margin: 0 10px;
  }

  .settings-bar {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .input-group {
    flex-direction: column;
  }

  .api-key-input {
    border-radius: 6px;
    margin-bottom: 10px;
  }

  .toggle-visibility {
    border-radius: 6px;
    border: 2px solid #ddd;
  }
}
