{"version": 3, "file": "number.mjs", "sourceRoot": "", "sources": ["../../../src/_vendor/zod-to-json-schema/parsers/number.ts"], "names": [], "mappings": "OACO,EAAE,eAAe,EAAiB,yBAAyB,EAAE;AAapE,MAAM,UAAU,cAAc,CAAC,GAAiB,EAAE,IAAU;IAC1D,MAAM,GAAG,GAA0B;QACjC,IAAI,EAAE,QAAQ;KACf,CAAC;IAEF,IAAI,CAAC,GAAG,CAAC,MAAM;QAAE,OAAO,GAAG,CAAC;IAE5B,KAAK,MAAM,KAAK,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;QAC/B,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,KAAK;gBACR,GAAG,CAAC,IAAI,GAAG,SAAS,CAAC;gBACrB,eAAe,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAClD,MAAM;YACR,KAAK,KAAK;gBACR,IAAI,IAAI,CAAC,MAAM,KAAK,aAAa,EAAE,CAAC;oBAClC,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;wBACpB,yBAAyB,CAAC,GAAG,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;oBAC9E,CAAC;yBAAM,CAAC;wBACN,yBAAyB,CAAC,GAAG,EAAE,kBAAkB,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;oBACvF,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;wBACrB,GAAG,CAAC,gBAAgB,GAAG,IAAW,CAAC;oBACrC,CAAC;oBACD,yBAAyB,CAAC,GAAG,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAC9E,CAAC;gBACD,MAAM;YACR,KAAK,KAAK;gBACR,IAAI,IAAI,CAAC,MAAM,KAAK,aAAa,EAAE,CAAC;oBAClC,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;wBACpB,yBAAyB,CAAC,GAAG,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;oBAC9E,CAAC;yBAAM,CAAC;wBACN,yBAAyB,CAAC,GAAG,EAAE,kBAAkB,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;oBACvF,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;wBACrB,GAAG,CAAC,gBAAgB,GAAG,IAAW,CAAC;oBACrC,CAAC;oBACD,yBAAyB,CAAC,GAAG,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAC9E,CAAC;gBACD,MAAM;YACR,KAAK,YAAY;gBACf,yBAAyB,CAAC,GAAG,EAAE,YAAY,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAC/E,MAAM;QACV,CAAC;IACH,CAAC;IACD,OAAO,GAAG,CAAC;AACb,CAAC"}