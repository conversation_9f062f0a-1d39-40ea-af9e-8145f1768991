import { useState } from 'react'
import FileUpload from './components/FileUpload'
import ResultsDisplay from './components/ResultsDisplay'
import { extractTextFromFile } from './utils/fileProcessor'
import { analyzeText } from './services/textAnalyzer'
import './App.css'

function App() {
  const [isProcessing, setIsProcessing] = useState(false)
  const [results, setResults] = useState(null)
  const [fileName, setFileName] = useState('')
  const [error, setError] = useState('')

  const handleFileUpload = async (file) => {
    setIsProcessing(true)
    setError('')
    setResults(null)
    setFileName(file.name)

    try {
      // Extract text from the uploaded file
      const extractedText = await extractTextFromFile(file)

      if (!extractedText || extractedText.trim().length === 0) {
        throw new Error('No text content found in the file')
      }

      // Analyze the extracted text
      const analysisResults = analyzeText(extractedText)
      setResults(analysisResults)

    } catch (err) {
      console.error('Error processing file:', err)
      setError(err.message || 'An error occurred while processing the file')
    } finally {
      setIsProcessing(false)
    }
  }

  const handleReset = () => {
    setResults(null)
    setFileName('')
    setError('')
  }

  return (
    <div className="app">
      <header className="app-header">
        <h1>📄 Document Analyzer</h1>
        <p>Upload any text file, PDF, or DOCX to extract important points automatically</p>
      </header>

      <main className="app-main">
        {error && (
          <div className="error-message">
            <span className="error-icon">⚠️</span>
            <span>{error}</span>
            <button onClick={handleReset} className="error-close">×</button>
          </div>
        )}

        <FileUpload
          onFileUpload={handleFileUpload}
          isProcessing={isProcessing}
        />

        {results && (
          <ResultsDisplay
            results={results}
            fileName={fileName}
          />
        )}

        {!results && !isProcessing && !error && (
          <div className="welcome-message">
            <h2>🚀 Get Started</h2>
            <p>Upload a document to see its most important points extracted automatically using advanced text analysis.</p>
            <div className="features">
              <div className="feature">
                <span className="feature-icon">🎯</span>
                <span>Smart Point Extraction</span>
              </div>
              <div className="feature">
                <span className="feature-icon">🔍</span>
                <span>Keyword Analysis</span>
              </div>
              <div className="feature">
                <span className="feature-icon">📊</span>
                <span>Relevance Scoring</span>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  )
}

export default App
