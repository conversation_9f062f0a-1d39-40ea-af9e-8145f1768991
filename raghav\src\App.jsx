import { useState } from 'react'
import FileUpload from './components/FileUpload'
import ResultsDisplay from './components/ResultsDisplay'
import ApiKeyInput from './components/ApiKeyInput'
import { processFileWithOpenAI } from './services/openaiService'
import './App.css'

function App() {
  const [isProcessing, setIsProcessing] = useState(false)
  const [results, setResults] = useState(null)
  const [fileName, setFileName] = useState('')
  const [error, setError] = useState('')
  const [apiKey, setApiKey] = useState('')
  const [maxPoints, setMaxPoints] = useState(5)

  const handleApiKeySubmit = (key) => {
    setApiKey(key)
    setError('')
  }

  const handleFileUpload = async (file) => {
    if (!apiKey) {
      setError('Please set your OpenAI API key first')
      return
    }

    setIsProcessing(true)
    setError('')
    setResults(null)
    setFileName(file.name)

    try {
      // Process file with OpenAI
      const analysisResults = await processFileWithOpenAI(file, apiKey, maxPoints)
      setResults(analysisResults)

    } catch (err) {
      console.error('Error processing file:', err)
      setError(err.message || 'An error occurred while processing the file')
    } finally {
      setIsProcessing(false)
    }
  }

  const handleReset = () => {
    setResults(null)
    setFileName('')
    setError('')
  }

  return (
    <div className="app">
      <header className="app-header">
        <h1>🤖 AI Document Analyzer</h1>
        <p>Upload PDF or text files to extract the most important points using OpenAI</p>
      </header>

      <main className="app-main">
        {error && (
          <div className="error-message">
            <span className="error-icon">⚠️</span>
            <span>{error}</span>
            <button onClick={handleReset} className="error-close">×</button>
          </div>
        )}

        {!apiKey ? (
          <ApiKeyInput
            onApiKeySubmit={handleApiKeySubmit}
            isProcessing={isProcessing}
          />
        ) : (
          <>
            <div className="settings-bar">
              <div className="api-status">
                <span className="status-indicator">🟢</span>
                <span>OpenAI Connected</span>
                <button
                  onClick={() => setApiKey('')}
                  className="change-key-btn"
                >
                  Change Key
                </button>
              </div>

              <div className="max-points-setting">
                <label htmlFor="maxPoints">Max Points:</label>
                <select
                  id="maxPoints"
                  value={maxPoints}
                  onChange={(e) => setMaxPoints(parseInt(e.target.value))}
                  disabled={isProcessing}
                >
                  <option value={3}>3 Points</option>
                  <option value={5}>5 Points</option>
                  <option value={7}>7 Points</option>
                  <option value={10}>10 Points</option>
                </select>
              </div>
            </div>

            <FileUpload
              onFileUpload={handleFileUpload}
              isProcessing={isProcessing}
            />

            {results && (
              <ResultsDisplay
                results={results}
                fileName={fileName}
              />
            )}

            {!results && !isProcessing && !error && (
              <div className="welcome-message">
                <h2>🚀 Ready to Analyze</h2>
                <p>Upload a PDF or text document to extract the {maxPoints} most important points using AI analysis.</p>
                <div className="features">
                  <div className="feature">
                    <span className="feature-icon">🤖</span>
                    <span>AI-Powered Analysis</span>
                  </div>
                  <div className="feature">
                    <span className="feature-icon">🎯</span>
                    <span>Only Key Points</span>
                  </div>
                  <div className="feature">
                    <span className="feature-icon">⚡</span>
                    <span>Smart Extraction</span>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </main>
    </div>
  )
}

export default App
