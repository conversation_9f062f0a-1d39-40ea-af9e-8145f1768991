import React, { useState } from 'react';

const ApiKeyInput = ({ onApiKeySubmit, isProcessing }) => {
  const [apiKey, setApiKey] = useState('');
  const [showKey, setShowKey] = useState(false);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (apiKey.trim()) {
      onApiKeySubmit(apiKey.trim());
    }
  };

  return (
    <div className="api-key-container">
      <div className="api-key-card">
        <h3>🔑 OpenAI API Configuration</h3>
        <p>Enter your OpenAI API key to enable intelligent document analysis</p>
        
        <form onSubmit={handleSubmit} className="api-key-form">
          <div className="input-group">
            <input
              type={showKey ? 'text' : 'password'}
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              placeholder="sk-..."
              className="api-key-input"
              disabled={isProcessing}
              required
            />
            <button
              type="button"
              onClick={() => setShowKey(!showKey)}
              className="toggle-visibility"
              disabled={isProcessing}
            >
              {showKey ? '👁️' : '👁️‍🗨️'}
            </button>
          </div>
          
          <button 
            type="submit" 
            className="submit-btn"
            disabled={!apiKey.trim() || isProcessing}
          >
            {isProcessing ? 'Configuring...' : 'Set API Key'}
          </button>
        </form>

        <div className="api-key-info">
          <h4>How to get your OpenAI API Key:</h4>
          <ol>
            <li>Go to <a href="https://platform.openai.com/api-keys" target="_blank" rel="noopener noreferrer">OpenAI API Keys</a></li>
            <li>Sign in to your OpenAI account</li>
            <li>Click "Create new secret key"</li>
            <li>Copy and paste the key above</li>
          </ol>
          
          <div className="security-note">
            <strong>🔒 Security Note:</strong> Your API key is only stored temporarily in your browser session and is never saved permanently.
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApiKeyInput;
