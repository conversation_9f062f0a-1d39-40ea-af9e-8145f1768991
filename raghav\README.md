# 🤖 AI Document Analyzer

An intelligent document analysis application powered by OpenAI that extracts only the most important points from text documents.

## ✨ Features

- **🤖 AI-Powered Analysis**: Uses OpenAI's GPT models for intelligent text analysis
- **🎯 Selective Extraction**: Returns only the most important points (3-10 configurable)
- **📄 Multi-Format Support**: Upload PDF and .txt files for analysis
- **🔑 Secure API Integration**: Your OpenAI API key is stored only in browser session
- **📱 Responsive Design**: Works on desktop and mobile devices
- **💾 Export Results**: Download extracted points as text file

## 🚀 Getting Started

### Prerequisites
- Node.js (v14 or higher)
- OpenAI API key ([Get one here](https://platform.openai.com/api-keys))

### Installation
1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```
3. Start the development server:
   ```bash
   npm run dev
   ```
4. Open your browser to the provided localhost URL

### Usage
1. **Set API Key**: Enter your OpenAI API key when prompted
2. **Configure Points**: Choose how many important points to extract (3-10)
3. **Upload Document**: Drag & drop or click to upload a PDF or .txt file
4. **View Results**: See the most important points extracted by AI
5. **Export**: Download results as a text file

## 🔧 Configuration

- **Max Points**: Adjust the number of important points to extract
- **API Key**: Change your OpenAI API key anytime
- **File Size**: Maximum 10MB file size limit

## 🛡️ Security

- API keys are stored only in browser session memory
- No data is permanently stored on servers
- Files are processed client-side before sending to OpenAI

## 📝 Supported Formats

Currently supports:
- **.pdf files** - PDF documents with readable text
- **.txt files** - Plain text documents

*Note: PDF files are processed client-side using PDF.js for security*

## 🎯 How It Works

1. **Upload**: Select a PDF or text file from your device
2. **Process**: File content is sent to OpenAI's API
3. **Analyze**: AI identifies the most critical information
4. **Extract**: Returns only the most important points
5. **Display**: Shows results with importance levels and keywords

## 🔑 OpenAI API Key

You'll need an OpenAI API key to use this application:
1. Go to [OpenAI API Keys](https://platform.openai.com/api-keys)
2. Sign in to your account
3. Create a new secret key
4. Copy and paste it into the application

## 🚀 Built With

- **React** - Frontend framework
- **Vite** - Build tool
- **OpenAI API** - AI text analysis
- **Modern CSS** - Responsive styling
