// Common stop words to filter out
const STOP_WORDS = new Set([
  'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
  'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
  'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these',
  'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them',
  'my', 'your', 'his', 'her', 'its', 'our', 'their', 'what', 'which', 'who', 'when', 'where',
  'why', 'how', 'all', 'any', 'both', 'each', 'few', 'more', 'most', 'other', 'some', 'such',
  'no', 'nor', 'not', 'only', 'own', 'same', 'so', 'than', 'too', 'very', 'just', 'now'
]);

/**
 * Clean and tokenize text
 */
const tokenizeText = (text) => {
  return text
    .toLowerCase()
    .replace(/[^\w\s]/g, ' ')
    .split(/\s+/)
    .filter(word => word.length > 2 && !STOP_WORDS.has(word));
};

/**
 * Calculate word frequency
 */
const calculateWordFrequency = (tokens) => {
  const frequency = {};
  tokens.forEach(token => {
    frequency[token] = (frequency[token] || 0) + 1;
  });
  return frequency;
};

/**
 * Split text into sentences
 */
const splitIntoSentences = (text) => {
  return text
    .split(/[.!?]+/)
    .map(sentence => sentence.trim())
    .filter(sentence => sentence.length > 20); // Filter out very short sentences
};

/**
 * Score sentences based on word frequency and other factors
 */
const scoreSentences = (sentences, wordFreq) => {
  return sentences.map(sentence => {
    const words = tokenizeText(sentence);
    let score = 0;
    let wordCount = 0;

    words.forEach(word => {
      if (wordFreq[word]) {
        score += wordFreq[word];
        wordCount++;
      }
    });

    // Normalize score by sentence length
    const normalizedScore = wordCount > 0 ? score / wordCount : 0;
    
    // Boost score for sentences with numbers (often contain facts)
    const hasNumbers = /\d/.test(sentence);
    const numberBoost = hasNumbers ? 1.2 : 1;
    
    // Boost score for sentences with capital words (proper nouns, important terms)
    const capitalWords = sentence.match(/\b[A-Z][a-z]+/g) || [];
    const capitalBoost = 1 + (capitalWords.length * 0.1);
    
    return {
      sentence: sentence.trim(),
      score: normalizedScore * numberBoost * capitalBoost,
      wordCount: words.length
    };
  });
};

/**
 * Extract keywords from text
 */
const extractKeywords = (text, limit = 10) => {
  const tokens = tokenizeText(text);
  const frequency = calculateWordFrequency(tokens);
  
  return Object.entries(frequency)
    .sort(([,a], [,b]) => b - a)
    .slice(0, limit)
    .map(([word, freq]) => ({ word, frequency: freq }));
};

/**
 * Main function to analyze text and extract important points
 */
export const analyzeText = (text) => {
  if (!text || text.trim().length === 0) {
    return {
      importantPoints: [],
      keywords: [],
      summary: {
        totalSentences: 0,
        totalWords: 0,
        selectedSentences: 0
      }
    };
  }

  // Basic text statistics
  const sentences = splitIntoSentences(text);
  const allWords = tokenizeText(text);
  const wordFreq = calculateWordFrequency(allWords);
  
  // Score and rank sentences
  const scoredSentences = scoreSentences(sentences, wordFreq);
  
  // Sort by score and take top sentences (max 10 or 20% of total sentences)
  const maxSentences = Math.min(10, Math.max(3, Math.ceil(sentences.length * 0.2)));
  const importantSentences = scoredSentences
    .sort((a, b) => b.score - a.score)
    .slice(0, maxSentences)
    .map((item, index) => ({
      id: index + 1,
      text: item.sentence,
      score: Math.round(item.score * 100) / 100,
      importance: item.score > 2 ? 'High' : item.score > 1 ? 'Medium' : 'Low'
    }));

  // Extract keywords
  const keywords = extractKeywords(text, 15);

  return {
    importantPoints: importantSentences,
    keywords: keywords,
    summary: {
      totalSentences: sentences.length,
      totalWords: allWords.length,
      selectedSentences: importantSentences.length
    }
  };
};
