import React, { useState, useRef } from 'react';

const FileUpload = ({ onFileUpload, isProcessing }) => {
  const [uploadedFile, setUploadedFile] = useState(null);
  const [isDragActive, setIsDragActive] = useState(false);
  const fileInputRef = useRef(null);

  const handleFileSelect = (file) => {
    // Check file type - support both PDF and text files
    const allowedTypes = ['text/plain', 'application/pdf'];
    const allowedExtensions = ['.txt', '.pdf'];
    const fileName = file.name.toLowerCase();
    const isValidType = allowedTypes.includes(file.type) ||
                       allowedExtensions.some(ext => fileName.endsWith(ext));

    if (!isValidType) {
      alert('Please upload a PDF or TXT file only.');
      return;
    }

    // Check file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      alert('File size must be less than 10MB.');
      return;
    }

    setUploadedFile(file);
    onFileUpload(file);
  };

  const handleFileInputChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDragOver = (event) => {
    event.preventDefault();
    setIsDragActive(true);
  };

  const handleDragLeave = (event) => {
    event.preventDefault();
    setIsDragActive(false);
  };

  const handleDrop = (event) => {
    event.preventDefault();
    setIsDragActive(false);

    const file = event.dataTransfer.files[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleClick = () => {
    if (!isProcessing) {
      fileInputRef.current?.click();
    }
  };

  return (
    <div className="file-upload-container">
      <div
        className={`dropzone ${isDragActive ? 'active' : ''} ${isProcessing ? 'processing' : ''}`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={handleClick}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept=".txt,.pdf"
          onChange={handleFileInputChange}
          style={{ display: 'none' }}
        />
        
        {isProcessing ? (
          <div className="processing-state">
            <div className="spinner"></div>
            <p>Processing your file...</p>
          </div>
        ) : (
          <div className="upload-content">
            <div className="upload-icon">📄</div>
            {uploadedFile ? (
              <div className="file-info">
                <p className="file-name">{uploadedFile.name}</p>
                <p className="file-size">{(uploadedFile.size / 1024).toFixed(2)} KB</p>
                <p className="upload-instruction">Click or drag to replace file</p>
              </div>
            ) : (
              <div className="upload-instructions">
                <h3>Upload your document</h3>
                <p>
                  {isDragActive
                    ? "Drop your file here..."
                    : "Drag & drop a file here, or click to select"}
                </p>
                <p className="supported-formats">
                  Supported formats: PDF, TXT (Max 10MB)
                </p>
              </div>
            )}
          </div>
        )}
      </div>
      
      {uploadedFile && !isProcessing && (
        <div className="file-actions">
          <button 
            className="clear-btn"
            onClick={() => {
              setUploadedFile(null);
            }}
          >
            Clear File
          </button>
        </div>
      )}
    </div>
  );
};

export default FileUpload;
