import React, { useState } from 'react';

const ResultsDisplay = ({ results, fileName }) => {
  const [activeTab, setActiveTab] = useState('points');

  if (!results) {
    return null;
  }

  const { importantPoints, keywords, summary } = results;

  const getImportanceColor = (importance) => {
    switch (importance) {
      case 'High': return '#e74c3c';
      case 'Medium': return '#f39c12';
      case 'Low': return '#3498db';
      default: return '#95a5a6';
    }
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text).then(() => {
      // You could add a toast notification here
      console.log('Copied to clipboard');
    });
  };

  const exportResults = () => {
    const exportText = `Important Points from ${fileName}\n\n` +
      importantPoints.map((point, index) => 
        `${index + 1}. ${point.text}`
      ).join('\n\n') +
      `\n\nKeywords: ${keywords.map(k => k.word).join(', ')}`;
    
    const blob = new Blob([exportText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `important-points-${fileName}.txt`;
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="results-container">
      <div className="results-header">
        <h2>Analysis Results</h2>
        <div className="file-info">
          <span className="file-name">📄 {fileName}</span>
        </div>
      </div>



      <div className="tabs">
        <button
          className={`tab ${activeTab === 'points' ? 'active' : ''}`}
          onClick={() => setActiveTab('points')}
        >
          Important Points
        </button>
        <button
          className={`tab ${activeTab === 'keywords' ? 'active' : ''}`}
          onClick={() => setActiveTab('keywords')}
        >
          Keywords
        </button>
      </div>

      <div className="tab-content">
        {activeTab === 'points' && (
          <div className="important-points">
            <div className="section-header">
              <h3>Key Points Extracted</h3>
              <button className="export-btn" onClick={exportResults}>
                📥 Export Results
              </button>
            </div>
            
            {importantPoints.length === 0 ? (
              <div className="no-results">
                <p>No important points could be extracted from this document.</p>
              </div>
            ) : (
              <div className="points-list">
                {importantPoints.map((point) => (
                  <div key={point.id} className="point-item">
                    <div className="point-header">
                      <span className="point-number">#{point.id}</span>
                      <span 
                        className="importance-badge"
                        style={{ backgroundColor: getImportanceColor(point.importance) }}
                      >
                        {point.importance}
                      </span>
                      <button 
                        className="copy-btn"
                        onClick={() => copyToClipboard(point.text)}
                        title="Copy to clipboard"
                      >
                        📋
                      </button>
                    </div>
                    <p className="point-text">{point.text}</p>
                    <div className="point-score">
                      Relevance Score: {point.score}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {activeTab === 'keywords' && (
          <div className="keywords-section">
            <h3>Most Frequent Keywords</h3>
            {keywords.length === 0 ? (
              <div className="no-results">
                <p>No keywords could be extracted from this document.</p>
              </div>
            ) : (
              <div className="keywords-grid">
                {keywords.map((keyword, index) => (
                  <div key={index} className="keyword-item">
                    <span className="keyword-text">{keyword.word}</span>
                    <span className="keyword-frequency">{keyword.frequency}</span>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ResultsDisplay;
